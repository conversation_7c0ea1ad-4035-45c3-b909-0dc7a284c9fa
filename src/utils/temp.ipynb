from plots import Plotter
plotter = Plotter()

from funcs import get_raw_data, compute_psi, compute_invariants, compute_deviatoric, compute_eps_pl, pre_process_data
import numpy as np
raw_data = get_raw_data('12.5',2e5)
pre_processed_data = pre_process_data(raw_data)
# val_data = pre_process_data(get_raw_data('12.3',2e5))
# np.all(np.diff(pre_processed_data[:, 2], axis=0) <= 0)



plotter.plot_data_scatter(pre_processed_data, axis_dict={'x':1, 'y':3, 'z':8})

raw_data = get_raw_data('12.3',2e5)
sigma = raw_data[:, 6:12]
epsilon = raw_data[:, 0:6]

def compute_eps_pl (epsilon, sigma, E=2e5, nu=0.3):
    S = np.array([                              # Compliance matrix
        [1, -nu, -nu, 0, 0, 0],
        [-nu, 1, -nu, 0, 0, 0],
        [-nu, -nu, 1, 0, 0, 0],
        [0, 0, 0, 2*(1+nu), 0, 0],
        [0, 0, 0, 0, 2*(1+nu), 0],
        [0, 0, 0, 0, 0, 2*(1+nu)]
    ]) / E
    delta_sigma = np.diff(sigma, axis=0)            # incremental stress tensor
    delta_eps_el = np.matmul(delta_sigma, S.T)      # incremental elastic strain tensor
    delta_eps = np.diff(epsilon, axis=0)            # incremental strain tensor
    delta_eps_pl = delta_eps - delta_eps_el

    eps_el = np.matmul(sigma, S.T)                  # elastic strain tensor
    eps_pl = epsilon - eps_el                       # plastic strain tensor
    print(np.isclose(np.diff(eps_pl, axis=0), delta_eps_pl))

    return delta_eps_pl, np.diff(eps_pl, axis=0)