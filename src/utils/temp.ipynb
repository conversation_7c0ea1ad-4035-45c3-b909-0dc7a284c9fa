from plots import Plotter
plotter = Plotter()

from funcs import get_raw_data, compute_psi, compute_invariants, compute_deviatoric, compute_eps_pl, pre_process_data
import numpy as np
raw_data = get_raw_data('12.3',2e5)
pre_processed_data = pre_process_data(raw_data)
# val_data = pre_process_data(get_raw_data('12.3',2e5))
np.all(np.diff(pre_processed_data[:, 2], axis=0) <= 0)



plotter.plot_data_scatter(pre_processed_data, axis_dict={'x':1, 'y':3, 'z':8})

raw_data = get_raw_data('12.3',2e5)
sigma = raw_data[:, 6:12]

delta_sigma = np.diff(sigma, axis=0)

print(delta_sigma.shape, sigma.shape)