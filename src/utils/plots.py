"""
This module contains plotting utilities for the project.

Date: 2025-07-09
Author: <PERSON><PERSON><PERSON>
"""

#%% ---- Import Required Libraries ----
import matplotlib.pyplot as plt
import numpy as np
import plotly.graph_objects as go
from mpl_toolkits.mplot3d import Axes3D

from matplotlib.ticker import ScalarFormatter


class Plotter:
    def __init__ (self):
        plt.rcParams["font.family"] = "serif"
        plt.rc('axes.formatter', use_mathtext=True)
        plt.rcParams["font.serif"] = "cmr10"
        plt.rcParams['font.size']=12

    def plot_data_scatter_plotly (self, train_data, val_data=None, test_data=None, axis_dict:dict={'x':0, 'y':1, 'z':2}):
        """Plot a 3D scatter plot of the data.

        Parameters:
        - train_data (array-like): Training data.
        - val_data (array-like): Validation data (optional).
        - test_data (array-like): Test data (optional).
        - axis_dict (dict): Dictionary specifying the indices of the features to plot on each axis.
                NOTE: The values and their corresponding titles are: 0: eps_pl_t, 1: s_t, 2: psi_max_t
        """

        x_index = axis_dict['x']
        y_index = axis_dict['y']
        z_index = axis_dict['z']

        axis_titles = {0: r'$I_1^{pl}$', 1: r'$I_2^{pl}$', 2: r'$I_3^{pl}$',
                       3: r'$\sigma_{11}^{dev}$', 4: r'$\sigma_{22}^{dev}$', 5: r'$\sigma_{12}^{dev}$', 6: r'$\sigma_{23}^{dev}$', 7: r'$\sigma_{13}^{dev}$',
                       8: r'$\psi^{max}$'}

        fig = go.Figure()
        fig.add_trace(go.Scatter3d(x=train_data[:, x_index], y=train_data[:, y_index], z=train_data[:,  z_index],
                                mode='markers', marker=dict(size=4, color='blue', opacity=0.8), name='Training'))
        if val_data is not None:
            fig.add_trace(go.Scatter3d(x=val_data[:, x_index], y=val_data[:, y_index], z=val_data[:, z_index],
                                    mode='markers', marker=dict(size=4, color='red', opacity=0.8), name='Validation'))
        if test_data is not None:
            fig.add_trace(go.Scatter3d(x=test_data[:, x_index], y=test_data[:, y_index], z=test_data[:, z_index],
                                    mode='markers', marker=dict(size=4, color='green', opacity=0.8), name='Test'))
        fig.update_layout(scene=dict(xaxis_title=axis_titles[x_index], yaxis_title=axis_titles[y_index], zaxis_title=axis_titles[z_index]), width=752, height=750, showlegend=True)

        fig.show()

    def plot_data_scatter (self, train_data, val_data=None, test_data=None, axis_dict:dict={'x':0, 'y':1, 'z':2}):
        """Plot a 3D scatter plot of the data.

        Parameters:
        - train_data (array-like): Training data.
        - val_data (array-like): Validation data (optional).
        - test_data (array-like): Test data (optional).
        - axis_dict (dict): Dictionary specifying the indices of the features to plot on each axis.
        """

        x_index = axis_dict['x']
        y_index = axis_dict['y']
        z_index = axis_dict['z']

        axis_titles = {0: r'$I_1^{pl}$', 1: r'$I_2^{pl}$', 2: r'$I_3^{pl}$',
                       3: r'$\sigma_{11}^{dev}$', 4: r'$\sigma_{22}^{dev}$', 5: r'$\sigma_{12}^{dev}$', 6: r'$\sigma_{23}^{dev}$', 7: r'$\sigma_{13}^{dev}$',
                       8: r'$\psi^{max}$'}

        fig = plt.figure(figsize=(8, 8))
        ax = fig.add_subplot(projection='3d')
        ax.scatter(train_data[:, x_index], train_data[:, y_index], train_data[:, z_index], c='b', marker='o', s=10, label='Training')
        if val_data is not None:
            ax.scatter(val_data[:, x_index], val_data[:, y_index], val_data[:, z_index], c='r', marker='o', label='Validation')
        if test_data is not None:
            ax.scatter(test_data[:, x_index], test_data[:, y_index], test_data[:, z_index], c='g', marker='o', label='Test')
        ax.set_xlabel(axis_titles[x_index])
        ax.set_ylabel(axis_titles[y_index])
        ax.set_zlabel(axis_titles[z_index])
        ax.legend()

        plt.show()

    def plot_loading_graphs (self, raw_data):
        """Plot the loading graphs for the raw data.\n
        The graph consists of 4 subplots:\n
        1. Strain components vs. increment\n
        2. Stress components vs. increment\n
        3. Strain components vs. Stress components\n
        4. State variable vs. increment

        Parameters:
        - raw_data (array-like): Raw data.
        """

        eps = raw_data[:, 0:6]
        sigma = raw_data[:, 6:12]
        sv = raw_data[:, 12]

        fig, ax = plt.subplots(2, 2, figsize=(7, 7))
        legend_fontsize = 8

        ax[0, 0].plot(eps[:, 0], label=r'$\epsilon_{11}$')
        ax[0, 0].plot(eps[:, 1], label=r'$\epsilon_{22}$')
        ax[0, 0].plot(eps[:, 2], label=r'$\epsilon_{33}$')
        ax[0, 0].plot(eps[:, 3], label=r'$\epsilon_{12}$')
        ax[0, 0].plot(eps[:, 4], label=r'$\epsilon_{23}$')
        ax[0, 0].plot(eps[:, 5], label=r'$\epsilon_{13}$')
        ax[0, 0].set_xlabel('Increment')
        ax[0, 0].set_ylabel(r'$\epsilon$' + ' (-)')
        ax[0, 0].ticklabel_format(axis='y', style='sci', scilimits=(0,0))
        ax[0, 0].legend(fontsize=legend_fontsize)
        ax[0, 0].grid(True, linestyle='--', alpha=0.25)

        ax[0, 1].plot(sigma[:, 0], label=r'$\sigma_{11}$')
        ax[0, 1].plot(sigma[:, 1], label=r'$\sigma_{22}$')
        ax[0, 1].plot(sigma[:, 2], label=r'$\sigma_{33}$')
        ax[0, 1].plot(sigma[:, 3], label=r'$\sigma_{12}$')
        ax[0, 1].plot(sigma[:, 4], label=r'$\sigma_{23}$')
        ax[0, 1].plot(sigma[:, 5], label=r'$\sigma_{13}$')
        ax[0, 1].set_xlabel('Increment')
        ax[0, 1].set_ylabel(r'$\sigma$' + ' (MPa)')
        ax[0, 1].legend(fontsize=legend_fontsize)
        ax[0, 1].grid(True, linestyle='--', alpha=0.25)

        ax[1, 0].plot(eps[:, 0], sigma[:, 0], label=r'$(\epsilon, \sigma)_{11}$')
        ax[1, 0].plot(eps[:, 1], sigma[:, 1], label=r'$(\epsilon, \sigma)_{22}$')
        ax[1, 0].plot(eps[:, 2], sigma[:, 2], label=r'$(\epsilon, \sigma)_{33}$')
        ax[1, 0].plot(eps[:, 3], sigma[:, 3], label=r'$(\epsilon, \sigma)_{12}$')
        ax[1, 0].plot(eps[:, 4], sigma[:, 4], label=r'$(\epsilon, \sigma)_{23}$')
        ax[1, 0].plot(eps[:, 5], sigma[:, 5], label=r'$(\epsilon, \sigma)_{13}$')
        ax[1, 0].set_xlabel(r'$\epsilon$' + ' (-)')
        ax[1, 0].set_ylabel(r'$\sigma $' + ' (MPa)')
        ax[1, 0].legend(fontsize=legend_fontsize)
        ax[1, 0].grid(True, linestyle='--', alpha=0.25)

        ax[1, 1].plot(sv, label=r'$\zeta$')
        ax[1, 1].set_xlabel('Increment')
        ax[1, 1].set_ylabel(r'$\zeta$' + ' (-)')
        ax[1, 1].legend(fontsize=legend_fontsize)
        ax[1, 1].ticklabel_format(axis='y', style='sci', scilimits=(0,0))
        ax[1, 1].grid(True, linestyle='--', alpha=0.25)

        fig.tight_layout()
        plt.show()
