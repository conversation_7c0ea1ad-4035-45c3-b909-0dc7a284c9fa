"""
This module contains plotting utilities for the project.

Date: 2025-07-09
Author: <PERSON><PERSON><PERSON>
"""


#%% ---- Import Required Libraries ----
import sys

import matplotlib.gridspec as gridspec
import matplotlib.pyplot as plt
import numpy as np
import plotly.graph_objects as go
from mpl_toolkits.mplot3d import Axes3D

sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src/utils')
from funcs import compute_error
from matplotlib.ticker import ScalarFormatter


class Plotter:
    def __init__ (self):
        plt.rcParams["font.family"] = "serif"
        plt.rc('axes.formatter', use_mathtext=True)
        plt.rcParams["font.serif"] = "cmr10"
        plt.rcParams['font.size']=12
#%% Data methods
    def plot_data_scatter_plotly (self, train_data, val_data=None, test_data=None, axis_dict:dict={'x':0, 'y':1, 'z':2}):
        """Plot a 3D scatter plot of the data.

        Parameters:
        - train_data (array-like): Training data.
        - val_data (array-like): Validation data (optional).
        - test_data (array-like): Test data (optional).
        - axis_dict (dict): Dictionary specifying the indices of the features to plot on each axis.
                NOTE: The values and their corresponding titles are: 0: eps_pl_t, 1: s_t, 2: psi_max_t
        """

        x_index = axis_dict['x']
        y_index = axis_dict['y']
        z_index = axis_dict['z']

        axis_titles = {0: r'$I_1^{pl}$', 1: r'$I_2^{pl}$', 2: r'$I_3^{pl}$',
                       3: r'$\sigma_{11}^{dev}$', 4: r'$\sigma_{22}^{dev}$', 5: r'$\sigma_{12}^{dev}$', 6: r'$\sigma_{23}^{dev}$', 7: r'$\sigma_{13}^{dev}$',
                       8: r'$\psi^{max}$'}

        fig = go.Figure()
        fig.add_trace(go.Scatter3d(x=train_data[:, x_index], y=train_data[:, y_index], z=train_data[:,  z_index],
                                mode='markers', marker=dict(size=4, color='blue', opacity=0.8), name='Training'))
        if val_data is not None:
            fig.add_trace(go.Scatter3d(x=val_data[:, x_index], y=val_data[:, y_index], z=val_data[:, z_index],
                                    mode='markers', marker=dict(size=4, color='red', opacity=0.8), name='Validation'))
        if test_data is not None:
            fig.add_trace(go.Scatter3d(x=test_data[:, x_index], y=test_data[:, y_index], z=test_data[:, z_index],
                                    mode='markers', marker=dict(size=4, color='green', opacity=0.8), name='Test'))
        fig.update_layout(scene=dict(xaxis_title=axis_titles[x_index], yaxis_title=axis_titles[y_index], zaxis_title=axis_titles[z_index]), width=752, height=750, showlegend=True)

        fig.show()

    def plot_data_scatter (self, train_data, val_data=None, test_data=None, axis_dict:dict={'x':0, 'y':1, 'z':2}):
        """Plot a 3D scatter plot of the data.

        Parameters:
        - train_data (array-like): Training data.
        - val_data (array-like): Validation data (optional).
        - test_data (array-like): Test data (optional).
        - axis_dict (dict): Dictionary specifying the indices of the features to plot on each axis.
        """

        x_index = axis_dict['x']
        y_index = axis_dict['y']
        z_index = axis_dict['z']

        axis_titles = {0: r'$J_2^{pl}$', 1: r'$J_3^{pl}$',
            2: r'$\sigma_{11}^{dev}$', 3: r'$\sigma_{22}^{dev}$', 4: r'$\sigma_{12}^{dev}$', 5: r'$\sigma_{23}^{dev}$', 6: r'$\sigma_{13}^{dev}$',
            7: r'$\psi^{dev, max}$'}

        fig = plt.figure(figsize=(8, 8))
        ax = fig.add_subplot(projection='3d')
        ax.scatter(train_data[:, x_index], train_data[:, y_index], train_data[:, z_index], c='b', marker='o', s=10, label='Training')
        if val_data is not None:
            ax.scatter(val_data[:, x_index], val_data[:, y_index], val_data[:, z_index], c='r', marker='o', s=10, label='Validation')
        if test_data is not None:
            ax.scatter(test_data[:, x_index], test_data[:, y_index], test_data[:, z_index], c='g', marker='o', label='Test')
        ax.set_xlabel(axis_titles[x_index], labelpad=15)
        ax.set_ylabel(axis_titles[y_index], labelpad=15)
        ax.set_zlabel(axis_titles[z_index], labelpad=15)
        ax.ticklabel_format(axis='both', style='sci', scilimits=(-2,2), useOffset=False, useMathText=True)
        ax.legend()

        plt.show()

    def plot_loading_graphs (self, raw_data):
        """Plot the loading graphs for the raw data.\n
        The graph consists of 4 subplots:\n
        1. Strain components vs. increment\n
        2. Stress components vs. increment\n
        3. Strain components vs. Stress components\n
        4. State variable vs. increment

        Parameters:
        - raw_data (array-like): Raw data.
        """

        eps = raw_data[:, 0:6]
        sigma = raw_data[:, 6:12]
        sv = raw_data[:, 12]

        fig, ax = plt.subplots(2, 2, figsize=(6.5, 5), dpi=120)
        legend_fontsize = 8

        ax[0, 0].plot(eps[:, 0], label=r'$\epsilon_{11}$')
        ax[0, 0].plot(eps[:, 1], label=r'$\epsilon_{22}$')
        ax[0, 0].plot(eps[:, 2], label=r'$\epsilon_{33}$')
        ax[0, 0].plot(eps[:, 3], label=r'$\epsilon_{12}$')
        ax[0, 0].plot(eps[:, 4], label=r'$\epsilon_{23}$')
        ax[0, 0].plot(eps[:, 5], label=r'$\epsilon_{13}$')
        ax[0, 0].set_xlabel('Increment')
        ax[0, 0].set_ylabel(r'$\epsilon$' + ' (-)')
        ax[0, 0].ticklabel_format(axis='y', style='sci', scilimits=(0,0))
        ax[0, 0].legend(fontsize=legend_fontsize)
        ax[0, 0].grid(True, linestyle='--', alpha=0.25)

        ax[0, 1].plot(sigma[:, 0], label=r'$\sigma_{11}$', alpha=0.75)
        ax[0, 1].plot(sigma[:, 1], label=r'$\sigma_{22}$', alpha=0.45)
        ax[0, 1].plot(sigma[:, 2], label=r'$\sigma_{33}$')
        ax[0, 1].plot(sigma[:, 3], label=r'$\sigma_{12}$')
        ax[0, 1].plot(sigma[:, 4], label=r'$\sigma_{23}$')
        ax[0, 1].plot(sigma[:, 5], label=r'$\sigma_{13}$')
        ax[0, 1].set_xlabel('Increment')
        ax[0, 1].set_ylabel(r'$\sigma$' + ' (MPa)')
        ax[0, 1].legend(fontsize=legend_fontsize)
        ax[0, 1].grid(True, linestyle='--', alpha=0.25)

        ax[1, 0].plot(eps[:, 0], sigma[:, 0], label=r'$(\epsilon, \sigma)_{11}$', alpha=0.75)
        ax[1, 0].plot(eps[:, 1], sigma[:, 1], label=r'$(\epsilon, \sigma)_{22}$', alpha=0.45)
        ax[1, 0].plot(eps[:, 2], sigma[:, 2], label=r'$(\epsilon, \sigma)_{33}$')
        ax[1, 0].plot(eps[:, 3], sigma[:, 3], label=r'$(\epsilon, \sigma)_{12}$')
        ax[1, 0].plot(eps[:, 4], sigma[:, 4], label=r'$(\epsilon, \sigma)_{23}$')
        ax[1, 0].plot(eps[:, 5], sigma[:, 5], label=r'$(\epsilon, \sigma)_{13}$')
        ax[1, 0].set_xlabel(r'$\epsilon$' + ' (-)')
        ax[1, 0].set_ylabel(r'$\sigma $' + ' (MPa)')
        # ax[1, 0].legend(fontsize=legend_fontsize)
        ax[1, 0].grid(True, linestyle='--', alpha=0.25)

        ax[1, 1].plot(sv, label=r'$\zeta$')
        ax[1, 1].set_xlabel('Increment')
        ax[1, 1].set_ylabel(r'$\zeta$' + ' (-)')
        ax[1, 1].legend(fontsize=legend_fontsize)
        ax[1, 1].ticklabel_format(axis='y', style='sci', scilimits=(0,0))
        ax[1, 1].grid(True, linestyle='--', alpha=0.25)

        fig.tight_layout()
        plt.show()

    def plot_data_hisograms (self, train_data, val_data=None, test_data=None,
                             columns_to_plot:list=None, custom_feature_names=None,
                             bins=30, figsize=(12, 8), use_log_scale_yaxis=False, save_path=None):
        """
        Plot histograms of selected data columns in a grid layout.

        Parameters:
        - train_data (array-like): Training data with shape [n_samples, n_features]
        - val_data (array-like): Validation data with shape [n_samples, n_features]
        - test_data (array-like, optional): Test data with shape [n_samples, n_features]
        - columns_to_plot (list, optional): Indices of columns to plot. If None, plots columns 0-4.
        - custom_feature_names (dict, optional): Custom names for specific features {column_index: name}.
                                               Overrides default names.
        - bins (int, optional): Number of bins for histograms. Default is 30.
        - figsize (tuple, optional): Figure size (width, height). Default is (8.5, 5).
        - use_log_scale_yaxis (bool, optional): Whether to use log scale for y-axis. Default is True.
        - save_path (str, optional): Path to save the figure. If None, figure is not saved.
        """
        # Define a standard mapping of column indices to feature names
        default_feature_names = {
            0: r'$J_2^{pl}$', 1: r'$J_3^{pl}$',
            2: r'$\sigma_{11}^{dev}$', 3: r'$\sigma_{22}^{dev}$', 4: r'$\sigma_{12}^{dev}$', 5: r'$\sigma_{23}^{dev}$', 6: r'$\sigma_{13}^{dev}$',
            7: r'$\psi^{max}$'
        }

        # Update default names with custom names if provided
        if custom_feature_names:
            default_feature_names.update(custom_feature_names)

        if columns_to_plot is None:
            columns_to_plot = list(default_feature_names.keys())

        n_features = len(columns_to_plot)

        n_cols = min(3, n_features)
        n_rows = (n_features + n_cols - 1) // n_cols

        fig = plt.figure(figsize=figsize, dpi=100)
        gs = gridspec.GridSpec(n_rows, n_cols, figure=fig)

        for i, col_idx in enumerate(columns_to_plot):
            ax = fig.add_subplot(gs[i // n_cols, i % n_cols])
            feature_name = default_feature_names.get(col_idx, f'Feature {col_idx}')

            # Determine common range for all datasets
            train_feature = train_data[:, col_idx]
            min_value = np.min(train_feature)
            max_value = np.max(train_feature)

            if val_data is not None:
                val_feature = val_data[:, col_idx]
                min_value = min(min_value, np.min(val_feature))
                max_value = max(max_value, np.max(val_feature))
            if test_data is not None:
                test_feature = test_data[:, col_idx]
                min_value = min(min_value, np.min(test_feature))
                max_value = max(max_value, np.max(test_feature))

            data_range = max_value - min_value
            if data_range < 1e-10:  # Almost constant values
                # Use a very small symmetric range around the mean
                mean_value = (min_value + max_value) / 2
                range_buffer = max(abs(mean_value) * 1e-6, 1e-10)
                hist_range = (mean_value - range_buffer, mean_value + range_buffer)
            else:
                range_buffer = data_range * 0.05 if data_range > 0 else abs(min_value) * 0.05
                hist_range = (min_value - range_buffer, max_value + range_buffer)

            ax.hist(train_feature, bins=bins, range=hist_range, histtype='step', alpha=0.8, label='Training', color='blue')
            if val_data is not None:
                ax.hist(val_feature, bins=bins, range=hist_range, histtype='stepfilled', alpha=0.5, label='Validation', color='crimson')
            if test_data is not None:
                ax.hist(test_feature, bins=bins, range=hist_range, histtype='stepfilled', edgecolor='black', alpha=0.65, label='Test', color='darkslategrey')
            ax.set_xlabel(feature_name)
            ax.set_ylabel(r'$\mathit{N}_{samples}$')
            if use_log_scale_yaxis: ax.set_yscale('log')
            self._set_minor_ticks(ax=ax, axis='x' if use_log_scale_yaxis else 'both', n_minor=3)
            ax.tick_params(axis='both', which='minor', length=1.3, width=0.8, labelsize=0)
            ax.ticklabel_format(axis='x', style='sci', scilimits=(-2,1), useMathText=True)

            # Only add legend to one subplot to save space
            if i == 7:
                ax.legend(loc='upper right', fontsize=9, frameon=False)
        plt.tight_layout()

        if save_path is not None:
            plt.savefig(f'{save_path}/data_histograms_grid.png', bbox_inches='tight', dpi=300)

        plt.show()

#%% Network methods
    def plot_loss (self, history, save_path=None):
        '''Plot the loss curve while training the network.'''
        history_dict = history.history
        loss = history_dict['loss']; val_loss = history_dict['val_loss']
        epochs = range(1, len(loss) + 1)

        fig_loss, ax_loss = plt.subplots(figsize=(6, 5))
        ax_loss.plot(epochs, loss, color='blue', label='train')
        ax_loss.scatter(epochs, val_loss, color='red', marker='o', s=7.5, alpha=.75, label='validation')
        ax_loss.set_title(f'Total loss curve'); ax_loss.set_xlabel('Epoch'); ax_loss.set_ylabel('Loss (MAE)')
        ax_loss.set_xscale('log'); ax_loss.set_yscale('log')
        ax_loss.legend(); ax_loss.grid(True, linestyle='--', alpha=0.5)

        for key in history_dict.keys():
            if 'output' in key and 'val_' not in key:
                val_key = 'val_' + key
                ax_loss.plot(epochs, history_dict[key], label=f'{key}_train')
                ax_loss.scatter(epochs, history_dict[val_key], marker='o', s=3.5, alpha=.5, label=f'{key}_val')
        fig_loss.tight_layout()
        if save_path is not None:
            fig_loss.savefig(fname=f'{save_path}/loss.png', bbox_inches='tight', dpi=500)
        plt.show()

    def plot_nn_psi_prediction (self, y_pred, y_true):

        error_type = 'mae'
        errors_dict = compute_error(y_pred=y_pred, y_true=y_true)
        avg_err = errors_dict[error_type]['average']
        error_values = errors_dict[error_type]['errors']

        fig = plt.figure(figsize=(5, 7.5))
        # fig = plt.figure(figsize=(3.5, 5))
        gs = fig.add_gridspec(2, 1, height_ratios=[1, 0.65])
        ax_pred = fig.add_subplot(gs[0, 0])
        ax_pred.plot(y_pred, color='blue', marker='o', markersize=2.5, markerfacecolor='white', markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0, label='Prediction')
        ax_pred.plot(y_true, color='lightblue', linewidth=2, alpha=0.7, label='True')
        ax_pred.set_xlabel('Increment')
        ax_pred.set_ylabel(r'$\psi^{dev, max}$' + ' (-)')
        ax_pred.ticklabel_format(style='sci', axis='x', scilimits=(0, 2))
        ax_pred.legend(fontsize=10)
        ax_pred.grid(True, linestyle='--', alpha=0.3)

        gs_down = gs[1, 0].subgridspec(1, 1)
        ax_err = fig.add_subplot(gs_down[0, 0])
        ax_err.plot(error_values, marker='o', color='red', markersize=2.5, linewidth=0, alpha=0.45)
        ax_err.set_ylabel('Absolute Error', fontsize=12)
        ax_err.set_xlabel('Increment', fontsize=12, loc='center')
        ax_err.text(x=0.03, y=0.95, s=f'{error_type.upper()} = {avg_err:.2e}', transform=ax_err.transAxes, fontsize=9, verticalalignment='top', bbox=dict(boxstyle='round', pad=0.25, edgecolor='gray', facecolor='yellow', alpha=0.6))
        ax_err.tick_params(axis='both', which='major', labelsize=11)
        ax_err.ticklabel_format(style='scientific', axis='y', scilimits=(-2, -1))
        # ax_err.yaxis.set_major_formatter(formatter)
        fig.tight_layout(rect=[0, 0, 1, 0.99])
        plt.show()

    @staticmethod
    def _set_minor_ticks(ax, axis='both', n_minor=4):
        """
        Set minor ticks automatically based on major tick spacing.

        Parameters:
        - ax: matplotlib axis object
        - axis: 'x', 'y', or 'both'
        - n_minor: number of minor ticks between major ticks
        """
        if axis in ['x', 'both']:
            major_ticks = ax.get_xticks()
            if len(major_ticks) > 1:
                major_spacing = major_ticks[1] - major_ticks[0]
                minor_spacing = major_spacing / (n_minor + 1)
                ax.xaxis.set_minor_locator(plt.MultipleLocator(minor_spacing))

        if axis in ['y', 'both']:
            major_ticks = ax.get_yticks()
            if len(major_ticks) > 1:
                major_spacing = major_ticks[1] - major_ticks[0]
                minor_spacing = major_spacing / (n_minor + 1)
                ax.yaxis.set_minor_locator(plt.MultipleLocator(minor_spacing))