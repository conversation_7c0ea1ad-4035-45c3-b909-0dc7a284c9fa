"""
This contains utility functions for the project.

Date: 2025-07-07
Author: <PERSON><PERSON><PERSON>
"""

#%% ---- Importing ----
import random
from pathlib import Path

import numpy as np

#%% ---- Set Global Dtype ----
GLOBAL_DTYPE = np.float32

#%% ---- Functions ----
# Data Operations
def get_raw_data(folder_name, E, file_names:dict=None):
    '''Read the data from the folder and return the raw data as a numpy array consisting
    strain(e11), stress(s11), state variable(sv1) and free energy.

    Parameters:
    - folder_name (str): The name of the folder containing the data.
    - E (float): Young's modulus.
    - file_names (dict): Dictionary containing the names of the data files.
        Default is None, which uses the default file names.

    Returns:
    - data (numpy.ndarray): Numpy array containing the raw data.
    '''

    if file_names is None:
        file_names = {
            'strain': 'strain.txt',
            'stress': 'stress.txt',
            'state_var': 'state_var.txt'
        }

    base_path = Path(__file__).parent
    data_path = base_path / '../../data' / folder_name

    if not data_path.exists():
        raise FileNotFoundError(f"Data folder '{data_path}' does not exist.")
    if not data_path.is_dir():
        raise NotADirectoryError(f"Path '{data_path}' is not a directory.")

    files = {
        name: data_path / file_name
        for name, file_name in file_names.items()
    }

    for name, file_path in files.items():
        if not file_path.exists():
            raise FileNotFoundError(f"File '{file_path}' does not exist.")
        if not file_path.is_file():
            raise IsADirectoryError(f"Path '{file_path}' is not a file.")

    try:
        strain = np.loadtxt(files['strain'], dtype=GLOBAL_DTYPE)
        stress = np.loadtxt(files['stress'], dtype=GLOBAL_DTYPE)
        state = np.loadtxt(files['state_var'], dtype=GLOBAL_DTYPE)
    except ValueError as e:
        raise ValueError(f"Error reading data files: {e}")

    n = len(strain)
    raw_data=np.empty((n, 13), dtype=GLOBAL_DTYPE)

    raw_data[:, 0:6] = strain[:, :]               # strain tensor (e11, e22, e33, e12, e23, e13, e21)
    raw_data[:, 6:12] = stress[:, :]              # stress tensor (s11, s22, s33, s12, s23, s13, s21)
    raw_data[:, 12] = state[:, 0]                   # state variable scalar (sv1)

    return raw_data

def compute_psi (stress, E=2e5, nu=0.3):
    '''Compute the free energy (psi) from the stress tensor.

    Parameters:
    - stress (numpy.ndarray): Numpy array containing the stress tensor with shape (n, 6).
    - E (float): Young's modulus.
    - nu (float): Poisson's ratio.

    Returns:
    - psi (numpy.ndarray): Numpy array containing the free energy with shape (n,).
    '''

    S = np.array([                              # Compliance matrix
        [1, -nu, -nu, 0, 0, 0],
        [-nu, 1, -nu, 0, 0, 0],
        [-nu, -nu, 1, 0, 0, 0],
        [0, 0, 0, 2*(1+nu), 0, 0],
        [0, 0, 0, 0, 2*(1+nu), 0],
        [0, 0, 0, 0, 0, 2*(1+nu)]
    ]) / E

    psi = 0.5 * np.einsum('ij,jk,ik->i', stress, S, stress)

    return psi

def compute_eps_pl (epsilon, sigma, E=2e5, nu=0.3):
    '''Compute the plastic strain tensor from the strain and stress tensors.

    Parameters:
    - epsilon (numpy.ndarray): Numpy array containing the strain tensor with shape (n, 6).
    - sigma (numpy.ndarray): Numpy array containing the stress tensor with shape (n, 6).
    - E (float): Young's modulus.
    - nu (float): Poisson's ratio.

    Returns:
    - eps_pl (numpy.ndarray): Numpy array containing the plastic strain tensor with shape (n, 6).
    '''
    S = np.array([                              # Compliance matrix
        [1, -nu, -nu, 0, 0, 0],
        [-nu, 1, -nu, 0, 0, 0],
        [-nu, -nu, 1, 0, 0, 0],
        [0, 0, 0, 2*(1+nu), 0, 0],
        [0, 0, 0, 0, 2*(1+nu), 0],
        [0, 0, 0, 0, 0, 2*(1+nu)]
    ]) / E
    # delta_sigma = np.diff(sigma, axis=0)            # incremental stress tensor
    # delta_eps_el = np.matmul(delta_sigma, S.T)      # incremental elastic strain tensor
    # delta_eps = np.diff(epsilon, axis=0)            # incremental strain tensor
    # delta_eps_pl = delta_eps - delta_eps_el

    eps_el = np.matmul(sigma, S.T)                  # elastic strain tensor
    eps_pl = epsilon - eps_el                       # plastic strain tensor

    return eps_pl

def compute_deviatoric (tensor):
    '''Compute the deviatoric part of a symmetric second-order tensor.

    Parameters:
    - tensor (numpy.ndarray): Numpy array containing the tensor with shape (n, 6).

    Returns:
    - tensor_dev (numpy.ndarray): Numpy array containing the deviatoric part of the tensor with shape (n, 6).
    '''
    tensor_dev = tensor.copy()

    for i in range(len(tensor)):
        hydro = (tensor[i, 0] + tensor[i, 1] + tensor[i, 2]) / 3.0
        tensor_dev[i, 0] -= hydro
        tensor_dev[i, 1] -= hydro
        tensor_dev[i, 2] -= hydro

    return tensor_dev

def compute_invariants (tensor):
    ''' Compute the invariants of a symmetric second-order tensor.

    Parameters:
    - tensor (numpy.ndarray): Numpy array containing the tensor with shape (n, 6).

    Returns:
    - I1 (numpy.ndarray): Numpy array containing the first invariant of the tensor with shape (n,).
    - I2 (numpy.ndarray): Numpy array containing the second invariant of the tensor with shape (n,).
    - I3 (numpy.ndarray): Numpy array containing the third invariant of the tensor with shape (n,).
    '''

    I1 = tensor[:, 0] + tensor[:, 1] + tensor[:, 2]             # trace
    I2 = tensor[:, 0]*tensor[:, 1] + tensor[:, 1]*tensor[:, 2] + tensor[:, 2]*tensor[:, 0] - (tensor[:, 3]**2 + tensor[:, 4]**2 + tensor[:, 5]**2)
    I3 = tensor[:, 0]*tensor[:, 1]*tensor[:, 2]\
        + 2*tensor[:, 3]*tensor[:, 4]*tensor[:, 5]\
        - tensor[:, 0]*tensor[:, 3]**2\
        - tensor[:, 1]*tensor[:, 4]**2\
        - tensor[:, 2]*tensor[:, 5]**2

    return I1, I2, I3

def pre_process_data(raw_data, threshold=1e-8):
    '''Pre-process the raw data to get the inputs and targets of the network.\n
    The inputs are the plastic strain invariants and the deviatoric stress and the target is the maximum free energy.
    The maximum free energy is computed by taking the maximum of the free energy up to the current time step.
    The increments before the first plasticity step are discarded.

    Parameters:
    - raw_data (numpy.ndarray): Numpy array containing the raw data.
    - threshold (float): Threshold for detecting the first plasticity step.

    Returns:
    - pre_processed_data (numpy.ndarray): Numpy array containing the pre-processed data with shape (n, 9).
    '''
    E = 2e5
    nu = 0.3

    epsilon = raw_data[:, 0:6]                                      # full strain tensor (e11, e22, e33, e12, e23, e13)
    sigma = raw_data[:, 6:12]                                       # stress tensor (s11, s22, s33, s12, s23, s13)
    acc_eq_pl_eps = raw_data[:, 12]                                 # accumulated equivalent plastic strain

    # Preparing the inputs and targets:
    ## Inputs:
    sigma_dev = compute_deviatoric(sigma)
    #! NOTE: deviatoric stress tensor (s11, s22, s12, s23, s13, s21)
    #! s11 + s22 + s33 = 0 --> s33 = - (s11 + s22) -->>> 2/3 of diagonals are independent. --->>> s33 is dropped.
    sigma_dev = np.hstack((sigma_dev[:, 0:2], sigma_dev[:, 3:]))

    eps_pl = compute_eps_pl(epsilon, sigma, E, nu)
    delta_eps_pl = np.vstack((np.zeros((1, 6), dtype=GLOBAL_DTYPE), np.diff(eps_pl, axis=0)))   # incremental plastic strain tensor
    # eps_pl_I1, eps_pl_I2, eps_pl_I3 = compute_invariants(delta_eps_pl)    # invariants of the plastic strain tensor

    eps_pl_I1, eps_pl_I2, eps_pl_I3 = np.zeros((len(sigma_dev),), dtype=GLOBAL_DTYPE),\
                                        np.zeros((len(sigma_dev),), dtype=GLOBAL_DTYPE),\
                                        np.zeros((len(sigma_dev),), dtype=GLOBAL_DTYPE)
    I1, I2, I3 = 0, 0, 0
    for i in range(len(sigma_dev)):
        I1_tmp, I2_tmp, I3_tmp = compute_invariants(delta_eps_pl[i].reshape(1, 6))
        I1 += I1_tmp
        I2 += I2_tmp
        I3 += I3_tmp
        eps_pl_I1[i] = I1
        eps_pl_I2[i] = I2
        eps_pl_I3[i] = I3

    assert eps_pl_I1.shape == eps_pl_I2.shape == eps_pl_I3.shape == (len(delta_eps_pl),), f"Shape mismatch of plastic strain invariants: {eps_pl_I1.shape}, {eps_pl_I2.shape}, {eps_pl_I3.shape}"

    ## Targets:
    psi = compute_psi(sigma, E, nu)
    psi_max = np.maximum.accumulate(psi)

    first_plastic_idx = np.argmax(acc_eq_pl_eps > threshold)

    # drop the increments before the first plasticity step
    sigma_dev_valid = sigma_dev[first_plastic_idx:]
    eps_pl_I1_valid = eps_pl_I1[first_plastic_idx:].reshape(-1, 1)
    eps_pl_I2_valid = eps_pl_I2[first_plastic_idx:].reshape(-1, 1)
    eps_pl_I3_valid = eps_pl_I3[first_plastic_idx:].reshape(-1, 1)
    psi_max_valid = psi_max[first_plastic_idx:].reshape(-1, 1)

    assert len(sigma_dev_valid) == len(eps_pl_I1_valid) == len(psi_max_valid), f"Length mismatch of pre-processed data: {len(sigma_dev_valid)}, {len(eps_pl_I1_valid)}, {len(psi_max_valid)}"

    n = len(sigma_dev_valid)
    pre_processed_data = np.empty((n, 9), dtype=GLOBAL_DTYPE)
    pre_processed_data = np.hstack((eps_pl_I1_valid, eps_pl_I2_valid, eps_pl_I3_valid, sigma_dev_valid, psi_max_valid))
    assert pre_processed_data.shape == (n, 9)

    return pre_processed_data

def process_data(train_data, val_data, test_data=None, eps=1e-8):
    '''Normalize the datasets using mixed normalization approach:
    - Invariants (columns 0-2): Component-wise min-max normalization [-1, 1]
    - Stress components (columns 3-7): Sample-wise normalization (unit vectors)
    - Target (column 8): Component-wise min-max normalization [-1, 1]

    Parameters:
    - train_data (array-like): Training data with shape (n_train, 9).
    - val_data (array-like): Validation data with shape (n_val, 9).
    - test_data (array-like, optional): Test data with shape (n_test, 9).
    - eps (float): Small epsilon for numerical stability.

    Returns:
    - n_train_data: Normalized training data.
    - n_val_data: Normalized validation data.
    - n_test_data: Normalized test data (if provided).
    - norm_params: Normalization parameters for invariants and target.
    '''
    assert train_data.shape[1] == val_data.shape[1] == 9, "Data should have 9 columns (8 inputs + 1 target)"

    num_train_samples = train_data.shape[0]
    TrVal_data = np.vstack((train_data, val_data))

    # Separate invariants, stress components, and psi
    invariants_data = TrVal_data[:, 0:3]
    stress_data = TrVal_data[:, 3:8]
    psi_data = TrVal_data[:, 8:9]

    n_invariants_data, invariants_norm_params = normalize_data(invariants_data, eps=eps)
    n_stress_data = normalize_stress_components(stress_data, eps=eps)
    n_psi_data, psi_norm_params = normalize_data(psi_data, eps=eps)

    # Reconstruct the normalized data
    n_TrVal_data = np.hstack((
        n_invariants_data,                  # normalized invariants
        n_stress_data,                      # normalized stress components
        n_psi_data                          # normalized psi
    ))

    n_train_data = n_TrVal_data[:num_train_samples, :]
    n_val_data = n_TrVal_data[num_train_samples:, :]
    # norm_params = [invariants_norm_params, psi_norm_params]
    norm_params = np.vstack((invariants_norm_params, psi_norm_params), dtype=GLOBAL_DTYPE)

    if test_data is not None:
        assert test_data.shape[1] == 9, "Test data should have 9 columns"
        n_test_data = apply_mixed_normalization(test_data, norm_params, eps=eps)
        return n_train_data, n_val_data, n_test_data, norm_params
    else:
        return n_train_data, n_val_data, norm_params

def normalize_stress_components(stress_data, eps=1e-8):
    '''Apply sample-wise normalization to stress components.

    Parameters:
    - stress_data (numpy.ndarray): Stress component data with shape (n_samples, 5).
    - eps (float): Small epsilon to prevent division by zero.

    Returns:
    - normalized_stress (numpy.ndarray): Sample-wise normalized stress components.
    '''
    n_stress_data = np.zeros_like(stress_data, dtype=GLOBAL_DTYPE)

    for i in range(stress_data.shape[0]):
        stress_vector_i = stress_data[i, :]
        magnitude = np.sqrt(np.sum(stress_vector_i**2))
        n_stress_data[i, :] = stress_vector_i / (magnitude + eps)

    return n_stress_data

def apply_mixed_normalization(data, norm_params, eps=1e-8):
    '''Apply mixed normalization to new data using the given normalization parameters.\n
    This function is normally used to normalize a test data using the normalization parameters
    obtained from the (training + validation) data.

    Parameters:
    - data (numpy.ndarray): Data to normalize with shape (n_samples, 9).
    - norm_params (numpy.ndarray): Normalization parameters for invariants and target.
    - eps (float): Small epsilon for numerical stability.

    Returns:
    - normalized_data (numpy.ndarray): Normalized data.
    '''
    invariants_data = data[:, 0:3]
    stress_data = data[:, 3:8]
    psi_data = data[:, 8:9]

    n_invariants_data = apply_normalization(invariants_data, norm_params[0:3, :])
    n_psi_data = apply_normalization(psi_data, norm_params[3:4, :])

    # Apply sample-wise normalization to stress components
    n_stress_data = normalize_stress_components(stress_data, eps=eps)

    # Reconstruct the normalized data
    normalized_data = np.hstack((
        n_invariants_data,                  # normalized invariants
        n_stress_data,                      # normalized stress components
        n_psi_data                          # normalized psi
    ))

    return normalized_data

def normalize_data(data, eps=1e-8):
    '''Normalize the data using the Min-Max normalization and extract the normalizing parameters.'''
    norm_params = get_α_β(data, eps=eps)
    nrml_data = apply_normalization(data, norm_params)
    return nrml_data, norm_params

def get_α_β(data, norm=True, norm_01=False, no_norm=False, eps=1e-8):
    ''' Compute the normalization parameters (α & β).\n
        "no_norm" is for the case when normalizing/standardizing is not considered.\n
        "norm_01" is for the case when the scaling range is [0, 1].'''
    n_rows, n_cols = data.shape
    norm_params = list()

    for i in range(0, n_cols):
        u = data[:, i]
        if no_norm == False:
            if norm == True:
                if norm_01 == False:
                    u_max = np.max(u)
                    u_min = np.min(u)
                    ## DEV: For pure elastic datasets where Dz_min = Dz_max = 0
                    if np.abs(u_max - u_min) < eps:
                        α=1; β=u_min
                    else:
                        α = (u_max - u_min) / 2.; β = np.average((u_max, u_min))
                else: α = np.max(u); β = 0.
            elif norm == False:
                α = np.std(u, axis=0); β = np.mean(u, axis=0)
        else: α = 1.; β = 0.

        norm_params.append((GLOBAL_DTYPE(α), GLOBAL_DTYPE(β)))

    return np.array(norm_params, dtype=GLOBAL_DTYPE)

def apply_normalization(data, normal_params: list):
    '''Normalize the data using the given normalizing parameters.'''
    n_rows, n_cols = data.shape
    nrml_data = np.empty((n_rows, n_cols), dtype=GLOBAL_DTYPE)
    assert n_cols == len(normal_params), f"Number of columns in data ({n_cols}) does not match number of normalization parameters ({len(normal_params)})"

    for i in range(0, n_cols):
        u = data[:, i]
        α = normal_params[i][0]; β = normal_params[i][1]
        nrml_data[:, i] = (u - β) / α

    return nrml_data

# Input File Creators
def create_randomload_inpfile (material, no_inc, strain_range = 0.02, model_address='', no_state_vars=3, load_type=4,
                               folder='../../material_dvlp/driver/inp', file_name='drive_v3.inp'):
    """
    Create an input file for the driver code with random loading increments.

    Parameters:
    - material (numpy array): array of material properties
    material = [E, v, σ_y, K, eps_0, m]
    - no_inc (int): number of increments
    - strain_range (float): the range in which the random strain increments varies.
    For example, if strain_range=0.02, the random strain increments will be between -0.02 and 0.02.
    - model_adress (str): the address of the model file
    - no_state_vars (int): number of state variables
    - load_type (int): type of loading. NOTE! Refer to the driver code help for more information.
    - folder (str): the folder in which the input file will be saved
    file_name (str): the name of the input file
    """

    matstr = np.array2string(material)[1:-1]

    loading = ''
    for inc in range(1, no_inc+1):
        random_e = str(f"{random.uniform(-strain_range, strain_range):.7f}")
        if inc < no_inc:
            loading += ('1 4 5 6' + '\n' +
                        '1 ' + random_e + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
        else:
            loading += ('1 4 5 6' + '\n' +
                        '1 ' + random_e + ' 0.0 0.0 0.0 0.0 0.0 1.0')
            # the first number is 1
            # >> it means that it creats a series of delta_epsilons.


    txt = ('INCLUDE ' + model_address + '\n' +
           'STATE_VAR ' + str(no_state_vars) + '\n' +
           'LOAD_TYPE ' + str(load_type) + '\n' +
           'BEGIN_MATERIAL' + '\n' +
           matstr + '\n' +
           'END_MATERIAL' + '\n' +
           'BEGIN_LOAD' + '\n' +
           loading + '\n' +
           'END_LOAD')

    with open(folder + '/' + file_name, 'w') as file:
        file.write(txt)
        file.close()

def create_monoload_inpfile (material, no_inc, model_address='', no_state_vars=3, load_type=4,
                             folder='../../material_dvlp/driver/inp', file_name='drive_v3.inp'):
    """
    Create an input file for the driver code with monotonic loading increments.

    Parameters:
    - material (numpy array): array of material properties
    material = [E, v, σ_y, K, eps_0, m]
    - no_inc (lis): List of number of increments
    no_inc = [no_el_inc, no_pl_inc, no_unload_inc]
    - model_adress (str): the address of the model file
    - no_state_vars (int): number of state variables
    - load_type (int): type of loading. NOTE! Refer to the driver code help for more information.
    - folder (str): the folder in which the input file will be saved
    """
    no_el_inc = no_inc[0]
    no_pl_inc = no_inc[1]
    no_unload_inc = no_inc[2]
    eps_y = material[2]/ material[0]                # the yield strain
    matstr = np.array2string(material)[1:-1]
    loading=''

    eps_inc = str(f"{(eps_y / (1*no_el_inc)):.7f}")         # the fixed elastic strain increment
    eps_unload_inc = str(f"{(-eps_y / (1*no_el_inc)):.6f}") # the fixed unloading strain increment

    if no_unload_inc == False:
        for inc in range(1, no_el_inc + no_pl_inc + 1):
            if inc < no_el_inc + no_pl_inc:
                loading += ('1 4 5 6' + '\n' +
                            '1 ' + eps_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
            else:
                loading += ('1 4 5 6' + '\n' +
                            '1 ' + eps_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0')
    else:
        for inc in range(1, no_el_inc + no_pl_inc + 1):
            loading += ('1 4 5 6' + '\n' +
                        '1 ' + eps_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
        for inc in range(1, no_unload_inc + 1):
            if inc < no_unload_inc:
                loading += ('1 4 5 6' + '\n' +
                '1 ' + eps_unload_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
            else:
                loading += ('1 4 5 6' + '\n' +
                '1 ' + eps_unload_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0')

    txt = ('INCLUDE ' + model_address + '\n' +
        'STATE_VAR ' + str(no_state_vars) + '\n' +
        'LOAD_TYPE ' + str(load_type) + '\n' +
        'BEGIN_MATERIAL' + '\n' +
        matstr + '\n' +
        'END_MATERIAL' + '\n' +
        'BEGIN_LOAD' + '\n' +
        loading + '\n' +
        'END_LOAD')

    with open(folder + '/' + file_name, 'w') as file:
        file.write(txt)
        file.close()
