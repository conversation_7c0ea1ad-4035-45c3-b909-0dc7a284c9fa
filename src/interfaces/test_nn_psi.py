import sys

import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src')

from utils.funcs import get_raw_data, pre_process_data, process_data, normalize_data, normalize_stress_components, apply_mixed_normalization
from utils.nn_model import NN_Model
from utils.plots import Plotter

#%% ---- Load Data ----
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

model_dir = '../../saved_models/psi_max/D1_LR0.0003_BS32_E1500_HL1_N(48_1)_ACTrelu_2025-07-23_14-01-51'
loaded_model, metadata = NN_Model.load_model(model_dir)

#%% ---- Inference ----
E=2e5
test_data = pre_process_data(get_raw_data('1.4',E))
psi_max_pred = loaded_model.infer(test_data)

psi_max_true = test_data[:, 7:8]

plotter = Plotter()
plotter.plot_nn_psi_prediction(y_pred=psi_max_pred, y_true=psi_max_true)