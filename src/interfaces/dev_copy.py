#%%
import sys

sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src')

from utils.funcs import get_raw_data, pre_process_data, process_data
from utils.plots import Plotter

#%%
plotter = Plotter()
train_data_raw = get_raw_data('1.0',2e5)
train_data = pre_process_data(train_data_raw)
val_data = pre_process_data(get_raw_data('1.1',2e5))
test_data = pre_process_data(get_raw_data('1.2',2e5))
test_data2 = pre_process_data(get_raw_data('1.3',2e5))
test_data3 = pre_process_data(get_raw_data('1.4',2e5))

n_train_data, n_val_data, n_test_data, norm_params = process_data(train_data, val_data, test_data)

# plotter.plot_loading_graphs(train_data_raw)
# plotter.plot_loading_graphs(get_raw_data('2.1',2e5))
# plotter.plot_loading_graphs(get_raw_data('2.3',2e5))
# plotter.plot_data_hisograms(train_data, val_data, test_data, figsize=(8, 5), use_log_scale_yaxis=False)
plotter.plot_data_scatter(train_data , val_data, test_data, axis_dict={'x':0, 'y':2, 'z':7})
# plotter.plot_data_scatter(train_data , val_data, test_data2, axis_dict={'x':0, 'y':2, 'z':7})
# plotter.plot_data_scatter(train_data , val_data, test_data3, axis_dict={'x':0, 'y':2, 'z':7})

plotter.plot_data_scatter(n_train_data , n_val_data, n_test_data, axis_dict={'x':0, 'y':2, 'z':7})

