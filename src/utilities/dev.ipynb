import sys

sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src')

from data_operations import DataConfig, TrainNormalizer, DataCalculator, InferenceNormalizer
from utils.funcs import get_raw_data, pre_process_data, process_data

train_data = pre_process_data(get_raw_data('2.0',2e5))
val_data = pre_process_data(get_raw_data('2.1',2e5))
test_data = pre_process_data(get_raw_data('2.2',2e5))

n_train_data, n_val_data, n_test_data, norm_params = process_data(train_data, val_data, test_data)

config = DataConfig()
train_normalizer = TrainNormalizer(config)

norm_train, norm_val, norm_params2 = train_normalizer.fit_and_transform(train_data, val_data)

import numpy as np
print(np.all(n_train_data == norm_train))
print(np.all(n_val_data == norm_val))
print(np.all(norm_params == norm_params2))

train_normalizer.is_fitted()

train_normalizer.get_parameters()

infer_normalizer = InferenceNormalizer(config, norm_params)
norm_test = infer_normalizer.transform(test_data)

print(np.all(n_test_data == norm_test))

infer_normalizer.get_parameters() == train_normalizer.get_parameters()

