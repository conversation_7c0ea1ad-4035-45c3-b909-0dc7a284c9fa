import numpy as np
from dataclasses import dataclass

@dataclass
class DataConfig:
    """Configuration for the preprocessing pipeline."""
    # Material properties
    E: float = 2e5  # <PERSON>'s modulus
    nu: float = 0.3  # <PERSON><PERSON>on's ratio

    # Processing parameters
    plasticity_threshold: float = 1e-8
    normalization_eps: float = 1e-8
    global_dtype: np.dtype = np.float32

    # Data structure parameters
    expected_raw_columns: int = 13  # strain(6) + stress(6) + state(1)
    expected_processed_columns: int = 8  # invariants(2) + stress(5) + target(1)

class DataCalculator:
    """
    Handles tensor calculations and feature extraction for materials data.

    This class focuses on the mathematical operations needed to transform
    raw strain/stress data into neural network features. It combines related
    tensor operations while maintaining clear responsibilities.

    Responsibilities:
    - Deviatoric stress calculations
    - Plastic strain computations
    - Invariant calculations
    - Free energy computations
    """

    def __init__ (self, config:DataConfig):
        self.config = config
        self.E = config.E
        self.nu = config.nu
        self.G = self.E/(2*(1+self.nu))             # Shear modulus
        self.S = np.array([
            [1, -self.nu, -self.nu, 0, 0, 0],
            [-self.nu, 1, -self.nu, 0, 0, 0],
            [-self.nu, -self.nu, 1, 0, 0, 0],
            [0, 0, 0, 2*(1+self.nu), 0, 0],
            [0, 0, 0, 0, 2*(1+self.nu), 0],
            [0, 0, 0, 0, 0, 2*(1+self.nu)]
        ]) / self.E

    def compute_eps_pl_invariants (self, epsilon:np.ndarray, sigma:np.ndarray) -> tuple[np.ndarray, np.ndarray]:
        '''Compute the necessary plastic strain invariants (Eq. plastic strain, J3 invariant) from the strain and stress tensors.

        Parameters:
        - epsilon (numpy.ndarray): Numpy array containing the strain tensor with shape (n, 6).
        - sigma (numpy.ndarray): Numpy array containing the stress tensor with shape (n, 6).

        Returns:
        - tuple of (equivalent_plastic_strain, J3_invariant)
        '''

        eps_el = np.matmul(sigma, self.S.T)                  # elastic strain tensor
        eps_pl = epsilon - eps_el                            # plastic strain tensor

        delta_eps_pl = np.vstack((np.zeros((1, 6), dtype=self.config.global_dtype),
                                  np.diff(eps_pl, axis=0)))       # incremental plastic strain tensor

        eqp = np.zeros(len(delta_eps_pl), dtype=self.config.global_dtype)           # cumulative equivalent plastic strain
        J3 = np.zeros(len(delta_eps_pl), dtype=self.config.global_dtype)            # instantaneous J3
        cum_eps_p = np.zeros(6, dtype=self.config.global_dtype)                     # running total plastic strain

        for i, D_eps_pl in enumerate(delta_eps_pl):
            # 1) make 'D_eps_pl' deviatoric
            D_eps_pl_dev = self._compute_deviatoric(D_eps_pl.reshape(1, 6))

            # 2) get principal-invariant I2 → convert to J2
            _, I2_inc, _ = self._compute_invariants(D_eps_pl_dev)
            J2_inc = np.abs(I2_inc)

            # 3) von Mises increment & accumulate
            eq_delta_pl_eps = np.sqrt((4.0/3.0) * J2_inc)
            eqp[i] = eqp[i-1] + eq_delta_pl_eps if i > 0 else eq_delta_pl_eps

            # 4) update total plastic strain, recompute its deviatoric third‐invariant
            cum_eps_p += D_eps_pl
            eps_pl_dev = self._compute_deviatoric(cum_eps_p.reshape(1, 6))
            _, _, J3[i] = self._compute_invariants(eps_pl_dev)

        assert eqp.shape == J3.shape, f"Shape mismatch of plastic strain invariants: {eqp.shape}, {J3.shape}"

        return eqp, J3

    def compute_psi (self, sigma:np.ndarray) -> np.ndarray:
        '''Compute the free energy (psi) from the stress tensor.

        Parameters:
        - sigma (numpy.ndarray): Numpy array containing the stress tensor with shape (n, 6).

        Returns:
        - psi (numpy.ndarray): Numpy array containing the free energy with shape (n,).
        '''

        psi = 0.5 * np.einsum('ij,jk,ik->i', sigma, self.S, sigma)

        return psi

    def find_plasticity_onset (self, eqp:np.ndarray) -> int:
        '''Find the index of the first plasticity onset.

        Parameters:
        - eqp (numpy.ndarray): Numpy array containing the equivalent plastic strain with shape (n,).

        Returns:
        - idx (int): Index of the first plasticity onset.
        '''

        idx = np.argmax(eqp > self.config.plasticity_threshold)

        return idx

    def _compute_deviatoric (self, tensor:np.ndarray) -> np.ndarray:
        '''Compute the deviatoric part of a symmetric second-order tensor.

        Parameters:
        - tensor (numpy.ndarray): Numpy array containing the tensor with shape (n, 6).

        Returns:
        - tensor_dev (numpy.ndarray): Numpy array containing the deviatoric part of the tensor with shape (n, 6).
        '''

        tensor_dev = tensor.copy()

        for i in range(len(tensor)):
            hydro = (tensor[i, 0] + tensor[i, 1] + tensor[i, 2]) / 3.0
            tensor_dev[i, 0] -= hydro
            tensor_dev[i, 1] -= hydro
            tensor_dev[i, 2] -= hydro

        return tensor_dev

    def _compute_invariants (self, tensor:np.ndarray) -> tuple[np.ndarray, np.ndarray, np.ndarray]:
        ''' Compute the invariants of a symmetric second-order tensor.

        Parameters:
        - tensor (numpy.ndarray): Numpy array containing the tensor with shape (n, 6).

        Returns:
        - I1 (numpy.ndarray): Numpy array containing the first invariant of the tensor with shape (n,).
        - I2 (numpy.ndarray): Numpy array containing the second invariant of the tensor with shape (n,).
        - I3 (numpy.ndarray): Numpy array containing the third invariant of the tensor with shape (n,).
        '''

        I1 = tensor[:, 0] + tensor[:, 1] + tensor[:, 2]             # trace
        I2 = tensor[:, 0]*tensor[:, 1] + tensor[:, 1]*tensor[:, 2] + tensor[:, 2]*tensor[:, 0] - (tensor[:, 3]**2 + tensor[:, 4]**2 + tensor[:, 5]**2)
        I3 = tensor[:, 0]*tensor[:, 1]*tensor[:, 2]\
            + 2*tensor[:, 3]*tensor[:, 4]*tensor[:, 5]\
            - tensor[:, 0]*tensor[:, 3]**2\
            - tensor[:, 1]*tensor[:, 4]**2\
            - tensor[:, 2]*tensor[:, 5]**2

        return I1, I2, I3
