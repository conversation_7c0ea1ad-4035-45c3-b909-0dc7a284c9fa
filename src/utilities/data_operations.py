from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional, Tuple

import numpy as np


# ============================================================================
# CONFIGURATION
# ============================================================================
@dataclass
class DataConfig:
    """Configuration for the preprocessing pipeline."""
    # Material properties
    E: float = 2e5  # Young's modulus
    nu: float = 0.3  # Poisson's ratio

    # Processing parameters
    plasticity_threshold: float = 1e-8
    normalization_eps: float = 1e-8
    global_dtype: np.dtype = np.float32

    # Data structure parameters
    expected_raw_columns: int = 13  # strain(6) + stress(6) + state(1)
    expected_processed_columns: int = 8  # invariants(2) + stress(5) + target(1)


# ============================================================================
# DATA CALCULATION
# ============================================================================
class DataCalculator:
    """
    Handles tensor calculations and feature extraction for materials data.

    This class focuses on the mathematical operations needed to transform
    raw strain/stress data into neural network features. It combines related
    tensor operations while maintaining clear responsibilities.

    Responsibilities:
    - Deviatoric stress calculations
    - Plastic strain computations
    - Invariant calculations
    - Free energy computations
    """

    def __init__ (self, config:DataConfig):
        self.config = config
        self.E = config.E
        self.nu = config.nu
        self.G = self.E/(2*(1+self.nu))             # Shear modulus
        self.S = np.array([
            [1, -self.nu, -self.nu, 0, 0, 0],
            [-self.nu, 1, -self.nu, 0, 0, 0],
            [-self.nu, -self.nu, 1, 0, 0, 0],
            [0, 0, 0, 2*(1+self.nu), 0, 0],
            [0, 0, 0, 0, 2*(1+self.nu), 0],
            [0, 0, 0, 0, 0, 2*(1+self.nu)]
        ]) / self.E

    def compute_eps_pl_invariants (self, epsilon:np.ndarray, sigma:np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        '''Compute the necessary plastic strain invariants (Eq. plastic strain, J3 invariant) from the strain and stress tensors.

        Parameters:
        - epsilon (numpy.ndarray): Numpy array containing the strain tensor with shape (n, 6).
        - sigma (numpy.ndarray): Numpy array containing the stress tensor with shape (n, 6).

        Returns:
        - tuple of (equivalent_plastic_strain, J3_invariant)
        '''

        eps_el = np.matmul(sigma, self.S.T)                  # elastic strain tensor
        eps_pl = epsilon - eps_el                            # plastic strain tensor

        delta_eps_pl = np.vstack((np.zeros((1, 6), dtype=self.config.global_dtype),
                                  np.diff(eps_pl, axis=0)))       # incremental plastic strain tensor

        eqp = np.zeros(len(delta_eps_pl), dtype=self.config.global_dtype)           # cumulative equivalent plastic strain
        J3 = np.zeros(len(delta_eps_pl), dtype=self.config.global_dtype)            # instantaneous J3
        cum_eps_p = np.zeros(6, dtype=self.config.global_dtype)                     # running total plastic strain

        for i, D_eps_pl in enumerate(delta_eps_pl):
            # 1) make 'D_eps_pl' deviatoric
            D_eps_pl_dev = self._compute_deviatoric(D_eps_pl.reshape(1, 6))

            # 2) get principal-invariant I2 → convert to J2
            _, I2_inc, _ = self._compute_invariants(D_eps_pl_dev)
            J2_inc = np.abs(I2_inc)

            # 3) von Mises increment & accumulate
            eq_delta_pl_eps = np.sqrt((4.0/3.0) * J2_inc)
            eqp[i] = eqp[i-1] + eq_delta_pl_eps if i > 0 else eq_delta_pl_eps

            # 4) update total plastic strain, recompute its deviatoric third‐invariant
            cum_eps_p += D_eps_pl
            eps_pl_dev = self._compute_deviatoric(cum_eps_p.reshape(1, 6))
            _, _, J3[i] = self._compute_invariants(eps_pl_dev)

        assert eqp.shape == J3.shape, f"Shape mismatch of plastic strain invariants: {eqp.shape}, {J3.shape}"

        return eqp, J3

    def compute_psi (self, sigma:np.ndarray) -> np.ndarray:
        '''Compute the free energy (psi) from the stress tensor.

        Parameters:
        - sigma (numpy.ndarray): Numpy array containing the stress tensor with shape (n, 6).

        Returns:
        - psi (numpy.ndarray): Numpy array containing the free energy with shape (n,).
        '''

        psi = 0.5 * np.einsum('ij,jk,ik->i', sigma, self.S, sigma)

        return psi

    def find_plasticity_onset (self, eqp:np.ndarray) -> int:
        '''Find the index of the first plasticity onset.

        Parameters:
        - eqp (numpy.ndarray): Numpy array containing the equivalent plastic strain with shape (n,).

        Returns:
        - idx (int): Index of the first plasticity onset.
        '''

        idx = np.argmax(eqp > self.config.plasticity_threshold)

        return idx

    def _compute_deviatoric (self, tensor:np.ndarray) -> np.ndarray:
        '''Compute the deviatoric part of a symmetric second-order tensor.

        Parameters:
        - tensor (numpy.ndarray): Numpy array containing the tensor with shape (n, 6).

        Returns:
        - tensor_dev (numpy.ndarray): Numpy array containing the deviatoric part of the tensor with shape (n, 6).
        '''

        tensor_dev = tensor.copy()

        for i in range(len(tensor)):
            hydro = (tensor[i, 0] + tensor[i, 1] + tensor[i, 2]) / 3.0
            tensor_dev[i, 0] -= hydro
            tensor_dev[i, 1] -= hydro
            tensor_dev[i, 2] -= hydro

        return tensor_dev

    def _compute_invariants (self, tensor:np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        ''' Compute the invariants of a symmetric second-order tensor.

        Parameters:
        - tensor (numpy.ndarray): Numpy array containing the tensor with shape (n, 6).

        Returns:
        - I1 (numpy.ndarray): Numpy array containing the first invariant of the tensor with shape (n,).
        - I2 (numpy.ndarray): Numpy array containing the second invariant of the tensor with shape (n,).
        - I3 (numpy.ndarray): Numpy array containing the third invariant of the tensor with shape (n,).
        '''

        I1 = tensor[:, 0] + tensor[:, 1] + tensor[:, 2]             # trace
        I2 = tensor[:, 0]*tensor[:, 1] + tensor[:, 1]*tensor[:, 2] + tensor[:, 2]*tensor[:, 0] - (tensor[:, 3]**2 + tensor[:, 4]**2 + tensor[:, 5]**2)
        I3 = tensor[:, 0]*tensor[:, 1]*tensor[:, 2]\
            + 2*tensor[:, 3]*tensor[:, 4]*tensor[:, 5]\
            - tensor[:, 0]*tensor[:, 3]**2\
            - tensor[:, 1]*tensor[:, 4]**2\
            - tensor[:, 2]*tensor[:, 5]**2

        return I1, I2, I3


# ============================================================================
# NORMALIZATION CLASSES
# ============================================================================
class Normalizer (ABC):

    def __init__ (self, config:DataConfig):
        """
        :param config: Preprocessing configuration containing material and numerical parameters.
        """
        self.config = config

    def _compute_minmax_params (self, data: np.ndarray) -> np.ndarray:
        '''Compute min-max normalization parameters for [-1, 1] range.

        For each column, computes alpha and beta parameters where:
        - normalized = (original - beta) / alpha
        - alpha = (max - min) / 2
        - beta = (max + min) / 2

        Special case: If max == min (constant data), sets alpha=1, beta=min

        :param data: Input data array of shape (n_samples, n_features)
        :return: Parameter array of shape (n_features, 2) where each row is [alpha, beta]
        '''

        n_features = data.shape[1]
        params = np.zeros((n_features, 2), dtype=self.config.global_dtype)

        for i in range(n_features):
            col_data = data[:, i]
            col_max = np.max(col_data)
            col_min = np.min(col_data)

            # Handle constant data case
            if np.abs(col_max - col_min) < self.config.normalization_eps:
                alpha = 1.0
                beta = col_min
            else:
                alpha = (col_max - col_min) / 2.0
                beta = (col_max + col_min) / 2.0

            params[i, 0] = alpha
            params[i, 1] = beta

        return params

    def _apply_minmax_normalization (self, data: np.ndarray, norm_params: np.ndarray) -> np.ndarray:
        """ Apply min-max normalization using provided parameters.

        Applies the transformation: normalized = (original - beta) / alpha
        for each column using the corresponding parameter pair.

        Args:
            data: Input data to normalize (n_samples, n_features)
            norm_params: Normalization parameters (n_features, 2) where each row is [alpha, beta]

        Returns:
            Normalized data with same shape as input

        Raises:
            ValueError: If data and parameter dimensions don't match
        """

        if data.shape[1] != norm_params.shape[0]:
            raise ValueError(f"Data columns ({data.shape[1]}) must match "
                           f"parameter rows ({norm_params.shape[0]})")

        normalized = np.zeros_like(data, dtype=self.config.global_dtype)

        for i in range(data.shape[1]):
            alpha, beta = norm_params[i]
            normalized[:, i] = (data[:, i] - beta) / alpha

        return normalized

    def _apply_sample_wise_normalization (self, stress_data: np.ndarray) -> np.ndarray:
        """ Apply sample-wise normalization to stress components.

        Normalizes each sample (row) to a unit vector by dividing by its magnitude.
        This preserves the direction of the stress vector while normalizing its magnitude.

        Formula: normalized_stress = stress / (||stress|| + eps)
        where ||stress|| is the L2 norm and eps prevents division by zero.

        Args:
            stress_data: Stress component data (n_samples, 5)
                        Columns: [s11, s22, s12, s23, s13]

        Returns:
            Sample-wise normalized stress data with same shape as input
        """
        normalized = np.zeros_like(stress_data, dtype=self.config.global_dtype)

        for i in range(stress_data.shape[0]):
            stress_vector = stress_data[i, :]
            magnitude = np.sqrt(np.sum(stress_vector**2))
            normalized[i, :] = stress_vector / (magnitude + self.config.normalization_eps)

        return normalized

    def _apply_mixed_normalization (self, data: np.ndarray, norm_params: np.ndarray) -> np.ndarray:
        """ Apply the complete mixed normalization strategy.

        Applies different normalization strategies to different data components:
        - Columns 0-1 (invariants): Component-wise min-max [-1, 1]
        - Columns 2-6 (stress): Sample-wise unit vector normalization
        - Column 7 (target): Component-wise min-max [-1, 1]

        Args:
            data: Input data (n_samples, 8)
            norm_params: Normalization parameters (3, 2) for invariants and target

        Returns:
            Mixed normalized data with same shape as input
        """

        if data.shape[1] != self.config.expected_processed_columns:
            raise ValueError(f"Expected {self.config.expected_processed_columns} columns, "
                           f"got {data.shape[1]}")

        # separate the data components
        invariants_data = data[:, 0:2]              # invariants (eqp, J3)
        stress_data = data[:, 2:7]                  # stress components (s11, s22, s12, s23, s13)
        target_data = data[:, 7:8]                  # target (psi)

        # apply appropriate normalization to each component
        n_invariants_data = self._apply_minmax_normalization(invariants_data, norm_params[0:2, :])
        n_stress_data = self._apply_sample_wise_normalization(stress_data)
        n_target_data = self._apply_minmax_normalization(target_data, norm_params[2:3, :])

        # recombine the normalized components
        normalized_data = np.hstack((n_invariants_data, n_stress_data, n_target_data))

        return normalized_data

    @abstractmethod
    def transform (self, data: np.ndarray) -> np.ndarray:
        pass


class TrainNormalizer (Normalizer):
    """
    This class handles the normalization for the training workflow.

    Responsibilities:
    - Fit normalization parameters from training + validation data
    - Transform multiple datasets (train, validation, optional test)
    - Store fitted parameters for later use
    - Provide transform method for additional data

    Usage:
        normalizer = TrainNormalizer(config)
        train_norm, val_norm, params = normalizer.fit_and_transform(train_data, val_data)
        new_norm = normalizer.transform(new_data)
    """
    def __init__ (self, config:DataConfig):
        super().__init__(config)
        self.norm_params = None
        self._is_fitted = False

    def fit_and_transform (self, train_data: np.ndarray, val_data: np.ndarray,
                           test_data: Optional[np.ndarray] = None) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray], np.ndarray]:
        """ Fit normalization parameters on training+validation data and transform all datasets.
        1. Combines training and validation data for parameter fitting
        2. Computes normalization parameters for invariants and targets
        3. Applies mixed normalization to all provided datasets
        4. Returns normalized datasets and parameters

        Args:
            train_data: Training data (n_train, 8)
                       Columns: eqp(1) + J3(1) + stress_dev(5) + psi_max(1)
            val_data: Validation data (n_val, 8)
            test_data: Optional test data (n_test, 8)

        Returns:
            If test_data is None: (train_normalized, val_normalized, norm_params)
            If test_data provided: (train_normalized, val_normalized, test_normalized, norm_params)

        Raises:
            ValueError: If input data has incorrect shape
        """
        # Validate input shapes
        expected_cols = self.config.expected_processed_columns
        if train_data.shape[1] != expected_cols:
            raise ValueError(f"Training data must have {expected_cols} columns, "
                           f"got {train_data.shape[1]}")
        if val_data.shape[1] != expected_cols:
            raise ValueError(f"Validation data must have {expected_cols} columns, "
                           f"got {val_data.shape[1]}")
        if test_data is not None and test_data.shape[1] != expected_cols:
            raise ValueError(f"Test data must have {expected_cols} columns, "
                           f"got {test_data.shape[1]}")

        TrVal_data = np.vstack((train_data, val_data))

        # separate data components for parameter computation
        invariants_data = TrVal_data[:, 0:2]              # invariants (eqp, J3)
        target_data = TrVal_data[:, 7:8]                  # target (psi)

        # compute normalization parameters
        invariants_norm_params = self._compute_minmax_params(invariants_data)
        target_norm_params = self._compute_minmax_params(target_data)
        # store normalization parameters
        self.norm_params = np.vstack((invariants_norm_params, target_norm_params))
        self._is_fitted = True

        # transform all datasets
        results = []
        datasets = [train_data, val_data, test_data] if test_data is not None else [train_data, val_data]

        for dataset in datasets:
            normalized = self._apply_mixed_normalization(dataset, self.norm_params)
            results.append(normalized)

        if test_data is not None:
            return results[0], results[1], results[2], self.norm_params
        else:
            return results[0], results[1], self.norm_params

    def transform (self, data: np.ndarray) -> np.ndarray:
        """ Apply fitted normalization parameters to new data.

        This method can be used to normalize additional data using the same
        parameters that were fitted during fit_and_transform().

        Args:
            data: Input data to normalize (n_samples, 8)

        Returns:
            Normalized data with same shape as input

        Raises:
            ValueError: If normalizer hasn't been fitted or data has wrong shape
        """
        if not self._is_fitted:
            raise ValueError("Normalizer must be fitted before transforming data. "
                           "Call fit_and_transform() first.")

        if data.shape[1] != self.config.expected_processed_columns:
            raise ValueError(f"Data must have {self.config.expected_processed_columns} columns, "
                           f"got {data.shape[1]}")

        return self._apply_mixed_normalization(data, self.normalization_params)

    def get_parameters(self) -> np.ndarray:
        """ Get the fitted normalization parameters.

        Returns:
            Normalization parameters (3, 2) where:
            - Row 0: eqp parameters [alpha, beta]
            - Row 1: J3 parameters [alpha, beta]
            - Row 2: psi_max parameters [alpha, beta]

        Raises:
            ValueError: If normalizer hasn't been fitted
        """
        if not self._is_fitted:
            raise ValueError("Normalizer must be fitted before accessing parameters. "
                           "Call fit_and_transform() first.")

        return self.norm_params.copy()

    def is_fitted(self) -> bool:
        """ Check if the normalizer has been fitted.

        Returns:
            True if fit_and_transform() has been called, False otherwise
        """
        return self._is_fitted


class InferenceNormalizer (Normalizer):
    """Inference-time normalizer with pre-loaded parameters.

    This class handles the inference workflow where normalization parameters
    are provided at initialization (typically loaded from saved files) and
    used to transform new data without any fitting process.

    Responsibilities:
    - Accept pre-fitted normalization parameters at initialization
    - Transform new data using the provided parameters
    - Validate parameter format and compatibility
    - Provide consistent interface with training normalizer
    """

    def __init__(self, config: DataConfig, pre_fitted_norm_params: np.ndarray):
        """
        Args:
            config: Preprocessing configuration containing material properties
                   and numerical parameters
            pre_fitted_norm_params: Pre-computed normalization parameters (3, 2)
                             Row 0: eqp parameters [alpha, beta]
                             Row 1: J3 parameters [alpha, beta]
                             Row 2: psi_max parameters [alpha, beta]

        Raises:
            ValueError: If parameters have incorrect shape or invalid values
        """
        super().__init__(config)

        #validate parameter format
        self._validate_parameters(pre_fitted_norm_params)

        #store parameters
        self.norm_params = pre_fitted_norm_params.astype(self.config.global_dtype)
        self._is_ready = True
        pass

    def _validate_parameters(self, params: np.ndarray) -> None:
        """
        Validate the format and content of normalization parameters.

        Args:
            params: Parameters to validate

        Raises:
            ValueError: If parameters are invalid
        """
        if not isinstance(params, np.ndarray):
            raise ValueError(f"Parameters must be numpy array, got {type(params)}")

        if params.shape != (3, 2):
            raise ValueError(f"Parameters must have shape (3, 2), got {params.shape}")

        # Check for NaN or infinite values
        if not np.isfinite(params).all():
            raise ValueError("Parameters contain NaN or infinite values")

        # Check that alpha values (first column) are not zero
        alphas = params[:, 0]
        if np.any(np.abs(alphas) < self.config.normalization_eps):
            raise ValueError("Alpha parameters cannot be zero or near-zero")

    def transform (self, data: np.ndarray) -> np.ndarray:
        """
        Apply pre-fitted normalization parameters to input data.

        This method applies the mixed normalization strategy using the
        parameters provided at initialization:
        - Columns 0-1 (invariants): Component-wise min-max [-1, 1]
        - Columns 2-6 (stress): Sample-wise unit vector normalization
        - Column 7 (target): Component-wise min-max [-1, 1]

        Args:
            data: Input data to normalize (n_samples, 8)
                 Columns: eqp(1) + J3(1) + stress_dev(5) + psi_max(1)

        Returns:
            Normalized data with same shape as input

        Raises:
            ValueError: If data has incorrect shape
        """

        if not self._is_ready:
            raise ValueError("Normalizer is not ready for transformation")

        if data.shape[1] != self.config.expected_processed_columns:
            raise ValueError(f"Data must have {self.config.expected_processed_columns} columns, "
                           f"got {data.shape[1]}")

        return self._apply_mixed_normalization(data, self.norm_params)

    def get_parameters(self) -> np.ndarray:
        """
        Get the normalization parameters.

        Returns:
            Copy of normalization parameters (3, 2) where:
            - Row 0: eqp parameters [alpha, beta]
            - Row 1: J3 parameters [alpha, beta]
            - Row 2: psi_max parameters [alpha, beta]
        """
        return self.norm_params.copy()

    def is_ready(self) -> bool:
        """
        Check if the normalizer is ready for transformation.

        Returns:
            True if normalizer has valid parameters, False otherwise
        """
        return self._is_ready
